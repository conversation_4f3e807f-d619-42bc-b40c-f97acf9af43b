<template>
  <div class="change-phone-page">
    <!-- 状态栏 -->
    <div class="status-bar">
      <div class="status-left">
        <div class="signal-container">
          <img :src="signalIcon" alt="信号" class="signal-icon" />
          <img :src="signalBar1" alt="信号条1" class="signal-bar" />
          <img :src="signalBar2" alt="信号条2" class="signal-bar" />
          <img :src="signalBar3" alt="信号条3" class="signal-bar" />
        </div>
      </div>
      <div class="status-right">
        <div class="battery-container">
          <img :src="batteryOutline" alt="电池外框" class="battery-outline" />
          <img :src="batteryFill1" alt="电池电量" class="battery-fill" />
          <img :src="batteryFill2" alt="电池电量" class="battery-fill" />
          <img :src="batteryFill3" alt="电池电量" class="battery-fill" />
          <img :src="batteryFill4" alt="电池电量" class="battery-fill" />
          <img :src="batteryFill5" alt="电池电量" class="battery-fill" />
          <img :src="batteryFill6" alt="电池电量" class="battery-fill" />
          <img :src="batteryFill7" alt="电池电量" class="battery-fill" />
        </div>
      </div>
    </div>

    <!-- 返回按钮 -->
    <div class="back-button" @click="goBack">
      <img :src="arrowLeftPhone" alt="返回" class="arrow-left" />
    </div>

    <!-- 分隔线 -->
    <div class="separator"></div>

    <!-- 页面标题 -->
    <div class="page-title">更换手机号</div>

    <!-- 原手机号码输入框 -->
    <div class="textbox">
      <div class="textbox-label">原手机号码</div>
      <div class="textbox-divider"></div>
    </div>
    <div class="textbox-placeholder">请填写原手机号码</div>

    <!-- 新手机号码输入框 -->
    <div class="textbox new-phone">
      <div class="textbox-label">新手机号码</div>
      <div class="textbox-divider"></div>
    </div>
    <div class="textbox-placeholder new-phone-placeholder">请填写新手机号码</div>

    <!-- 下一步按钮 -->
    <div class="next-button" @click="nextStep">
      <span>下一步</span>
    </div>
  </div>
</template>

<script>
// 导入图片资源
import signalIcon from '../assets/images/signal-icon.svg'
import signalBar1 from '../assets/images/signal-bar1.svg'
import signalBar2 from '../assets/images/signal-bar2.svg'
import signalBar3 from '../assets/images/signal-bar3.svg'
import batteryOutline from '../assets/images/battery-outline.svg'
import batteryFill1 from '../assets/images/battery-fill1.svg'
import batteryFill2 from '../assets/images/battery-fill2.svg'
import batteryFill3 from '../assets/images/battery-fill3.svg'
import batteryFill4 from '../assets/images/battery-fill4.svg'
import batteryFill5 from '../assets/images/battery-fill5.svg'
import batteryFill6 from '../assets/images/battery-fill6.svg'
import batteryFill7 from '../assets/images/battery-fill7.svg'
import arrowLeftPhone from '../assets/images/arrow-left-phone.svg'

export default {
  name: 'ChangePhonePage',
  emits: ['go-back', 'next-step'],
  data() {
    return {
      // 状态栏图标
      signalIcon,
      signalBar1,
      signalBar2,
      signalBar3,
      batteryOutline,
      batteryFill1,
      batteryFill2,
      batteryFill3,
      batteryFill4,
      batteryFill5,
      batteryFill6,
      batteryFill7,
      // 返回按钮图标
      arrowLeftPhone
    }
  },
  methods: {
    goBack() {
      this.$emit('go-back')
    },
    nextStep() {
      this.$emit('next-step')
    }
  }
}
</script>

<style scoped>
.change-phone-page {
  width: 375px;
  height: 844px;
  background: #FFFFFF;
  position: relative;
  margin: 0 auto;
  box-shadow: 0px 3px 6px 0px rgba(18, 15, 40, 0.12);
  font-family: 'Inter', sans-serif;
  overflow-y: auto;
}

/* 状态栏 */
.status-bar {
  width: 100%;
  height: 40px;
  background: transparent;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 12px;
  box-sizing: border-box;
  border-bottom: 1px solid #BCC1CA;
}

.status-left {
  display: flex;
  align-items: center;
}

.signal-container {
  position: relative;
  width: 27.34px;
  height: 10.7px;
}

.signal-icon {
  position: absolute;
  left: 0;
  top: 0;
  width: 7.86px;
  height: 10.7px;
}

.signal-bar {
  position: absolute;
}

.signal-bar:nth-child(2) {
  left: 9.59px;
  top: 1.61px;
  width: 2.25px;
  height: 7.47px;
}

.signal-bar:nth-child(3) {
  left: 13.53px;
  top: 0.25px;
  width: 8.1px;
  height: 10.19px;
}

.signal-bar:nth-child(4) {
  left: 22.86px;
  top: 0.25px;
  width: 4.48px;
  height: 10.19px;
}

.status-right {
  display: flex;
  align-items: center;
}

.battery-container {
  position: relative;
  width: 65.87px;
  height: 10.56px;
}

.battery-outline {
  position: absolute;
  left: 43.05px;
  top: 0;
  width: 20.28px;
  height: 10.06px;
  opacity: 0.35;
}

.battery-fill {
  position: absolute;
}

.battery-fill:nth-child(2) {
  left: 64.68px;
  top: 3.75px;
  width: 1.19px;
  height: 3.59px;
  opacity: 0.4;
}

.battery-fill:nth-child(3) {
  left: 44.26px;
  top: 1.2px;
  width: 17.87px;
  height: 7.66px;
}

.battery-fill:nth-child(4) {
  left: 22.13px;
  top: 0.35px;
  width: 14.47px;
  height: 10.07px;
}

.battery-fill:nth-child(5) {
  left: 8.51px;
  top: 2.05px;
  width: 2.55px;
  height: 8.51px;
}

.battery-fill:nth-child(6) {
  left: 12.77px;
  top: 0.35px;
  width: 2.55px;
  height: 10.21px;
}

.battery-fill:nth-child(7) {
  left: 4.26px;
  top: 5.03px;
  width: 2.55px;
  height: 5.53px;
}

.battery-fill:nth-child(8) {
  left: 0;
  top: 7.16px;
  width: 2.55px;
  height: 3.4px;
}

/* 返回按钮 */
.back-button {
  position: absolute;
  left: 24px;
  top: 48px;
  width: 24px;
  height: 24px;
  cursor: pointer;
}

.arrow-left {
  width: 100%;
  height: 100%;
}

/* 分隔线 */
.separator {
  position: absolute;
  left: 0;
  top: 80px;
  width: 375px;
  height: 4px;
  background: #F8F9FA;
}

/* 页面标题 */
.page-title {
  position: absolute;
  left: 140px;
  top: 46px;
  width: 80px;
  height: 26px;
  font-family: Inter;
  font-weight: 400;
  font-size: 16px;
  line-height: 26px;
  color: #323842;
}

/* 输入框 */
.textbox {
  position: absolute;
  left: 24px;
  top: 124px;
  width: 327px;
  height: 36px;
  background: #FFFFFF;
  border: 1px solid transparent;
  border-radius: 6px 6px 0px 0px;
  display: flex;
  align-items: center;
  padding: 0 12px;
  box-sizing: border-box;
}

.textbox.new-phone {
  top: 178px;
}

.textbox-label {
  font-family: Inter;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  color: #171A1F;
}

.textbox-divider {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 327px;
  height: 1px;
  background: #F3F4F6;
}

.textbox-placeholder {
  position: absolute;
  left: 220px;
  top: 130px;
  width: 112px;
  height: 22px;
  font-family: Inter;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  color: #BCC1CA;
}

.textbox-placeholder.new-phone-placeholder {
  top: 185px;
}

/* 下一步按钮 */
.next-button {
  position: absolute;
  left: 13px;
  top: 715px;
  width: 350px;
  height: 52px;
  background: #636AE8;
  border-radius: 26px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0px 4px 9px 0px rgba(99, 106, 232, 0.11), 0px 0px 2px 0px rgba(99, 106, 232, 0.12);
}

.next-button span {
  font-family: Inter;
  font-weight: 400;
  font-size: 18px;
  line-height: 28px;
  color: #FFFFFF;
}
</style>
