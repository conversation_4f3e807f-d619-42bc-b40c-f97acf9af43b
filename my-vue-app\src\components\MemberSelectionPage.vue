<template>
  <div class="member-selection-page">
    <!-- 状态栏 -->
    <div class="status-bar">
      <div class="status-left">
        <div class="signal-container">
          <img :src="avatarIcon1" alt="信号" class="signal-icon" />
          <img :src="avatarIcon2" alt="信号条1" class="signal-bar" />
          <img :src="avatarIcon3" alt="信号条2" class="signal-bar" />
          <img :src="avatarIcon4" alt="信号条3" class="signal-bar" />
        </div>
      </div>
      <div class="status-right">
        <div class="battery-container">
          <img :src="avatarIcon5" alt="电池外框" class="battery-outline" />
          <img :src="avatarIcon6" alt="电池电量" class="battery-fill" />
          <img :src="avatarIcon7" alt="电池电量" class="battery-fill" />
          <img :src="avatarIcon8" alt="电池电量" class="battery-fill" />
          <img :src="avatarIcon9" alt="电池电量" class="battery-fill" />
          <img :src="avatarIcon10" alt="电池电量" class="battery-fill" />
          <img :src="avatarIcon11" alt="电池电量" class="battery-fill" />
          <img :src="avatarIcon12" alt="电池电量" class="battery-fill" />
        </div>
      </div>
    </div>

    <!-- 返回按钮 -->
    <div class="back-button" @click="goBack">
      <img :src="arrowLeft" alt="返回" />
    </div>

    <!-- 分隔线 -->
    <div class="separator"></div>

    <!-- 页面标题 -->
    <div class="page-title">选择成员关系</div>

    <!-- 成人身份选择 -->
    <div class="section-title">成人</div>
    <div class="tags-container">
      <div 
        v-for="role in adultRoles" 
        :key="role.id"
        class="tag-item"
        :class="{ active: selectedRole === role.id }"
        @click="selectRole(role.id)"
      >
        <div class="tag-frame">
          <span class="tag-text">{{ role.name }}</span>
        </div>
      </div>
    </div>

    <!-- 学生身份选择 -->
    <div class="section-title student-section">学生</div>
    <div class="tags-container">
      <div 
        v-for="role in studentRoles" 
        :key="role.id"
        class="tag-item"
        :class="{ active: selectedRole === role.id }"
        @click="selectRole(role.id)"
      >
        <div class="tag-frame">
          <span class="tag-text">{{ role.name }}</span>
        </div>
      </div>
    </div>

    <!-- 底部按钮 -->
    <div class="bottom-buttons">
      <button class="btn-wechat" @click="wechatInvite">
        <img :src="wechatIcon" alt="微信" class="btn-icon" />
        <span>微信邀请</span>
      </button>
      
      <button class="btn-manual" @click="manualAdd">
        <img :src="userPlusIcon" alt="添加" class="btn-icon" />
        <span>手动添加</span>
      </button>
    </div>
  </div>
</template>

<script>
// 导入图片资源
import avatarIcon1 from '../assets/images/avatar-icon-1.svg'
import avatarIcon2 from '../assets/images/avatar-icon-2.svg'
import avatarIcon3 from '../assets/images/avatar-icon-3.svg'
import avatarIcon4 from '../assets/images/avatar-icon-4.svg'
import avatarIcon5 from '../assets/images/avatar-icon-5.svg'
import avatarIcon6 from '../assets/images/avatar-icon-6.svg'
import avatarIcon7 from '../assets/images/avatar-icon-7.svg'
import avatarIcon8 from '../assets/images/avatar-icon-8.svg'
import avatarIcon9 from '../assets/images/avatar-icon-9.svg'
import avatarIcon10 from '../assets/images/avatar-icon-10.svg'
import avatarIcon11 from '../assets/images/avatar-icon-11.svg'
import avatarIcon12 from '../assets/images/avatar-icon-12.svg'
import arrowLeft from '../assets/images/arrow-left.svg'
import userPlusIcon from '../assets/images/user-plus-icon.svg'
import wechatIcon from '../assets/images/wechat-icon.svg'

export default {
  name: 'MemberSelectionPage',
  emits: ['go-back', 'member-selected', 'wechat-invite', 'manual-add'],
  data() {
    return {
      // 图片资源
      avatarIcon1,
      avatarIcon2,
      avatarIcon3,
      avatarIcon4,
      avatarIcon5,
      avatarIcon6,
      avatarIcon7,
      avatarIcon8,
      avatarIcon9,
      avatarIcon10,
      avatarIcon11,
      avatarIcon12,
      arrowLeft,
      userPlusIcon,
      wechatIcon,
      
      // 选中的身份
      selectedRole: 'dad',
      
      // 成人身份选项
      adultRoles: [
        { id: 'mom', name: '妈妈' },
        { id: 'dad', name: '爸爸' },
        { id: 'grandpa', name: '爷爷' },
        { id: 'grandma', name: '奶奶' },
        { id: 'grandfather', name: '外公' },
        { id: 'grandmother', name: '外婆' }
      ],
      
      // 学生身份选项
      studentRoles: [
        { id: 'baby', name: '宝贝' },
        { id: 'brother', name: '哥哥' },
        { id: 'sister', name: '姐姐' },
        { id: 'younger-brother', name: '弟弟' },
        { id: 'younger-sister', name: '妹妹' }
      ]
    }
  },
  methods: {
    goBack() {
      this.$emit('go-back')
    },
    selectRole(roleId) {
      this.selectedRole = roleId
    },
    wechatInvite() {
      this.$emit('wechat-invite', this.selectedRole)
    },
    manualAdd() {
      this.$emit('manual-add', this.selectedRole)
    }
  }
}
</script>

<style scoped>
.member-selection-page {
  width: 375px;
  height: 844px;
  background: #FFFFFF;
  position: relative;
  margin: 0 auto;
  box-shadow: 0px 3px 6px 0px rgba(18, 15, 40, 0.12);
  font-family: 'Inter', sans-serif;
  overflow-y: auto;
}

/* 状态栏 */
.status-bar {
  width: 100%;
  height: 40px;
  background: transparent;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 12px;
  box-sizing: border-box;
}

.status-left {
  display: flex;
  align-items: center;
}

.signal-container {
  position: relative;
  width: 27.34px;
  height: 10.7px;
}

.signal-icon {
  position: absolute;
  left: 0;
  top: 0;
  width: 7.86px;
  height: 10.7px;
}

.signal-bar {
  position: absolute;
}

.signal-bar:nth-child(2) {
  left: 9.59px;
  top: 1.61px;
  width: 2.25px;
  height: 7.47px;
}

.signal-bar:nth-child(3) {
  left: 13.53px;
  top: 0.25px;
  width: 8.1px;
  height: 10.19px;
}

.signal-bar:nth-child(4) {
  left: 22.86px;
  top: 0.25px;
  width: 4.48px;
  height: 10.19px;
}

.status-right {
  display: flex;
  align-items: center;
}

.battery-container {
  position: relative;
  width: 65.87px;
  height: 10.56px;
}

.battery-outline {
  position: absolute;
  left: 43.05px;
  top: 0;
  width: 20.28px;
  height: 10.06px;
  opacity: 0.35;
}

.battery-fill {
  position: absolute;
}

.battery-fill:nth-child(3) {
  left: 44.26px;
  top: 1.2px;
  width: 17.87px;
  height: 7.66px;
}

/* 返回按钮 */
.back-button {
  position: absolute;
  left: 24px;
  top: 48px;
  width: 24px;
  height: 24px;
  cursor: pointer;
}

.back-button img {
  width: 100%;
  height: 100%;
}

/* 分隔线 */
.separator {
  position: absolute;
  left: 0;
  top: 80px;
  width: 375px;
  height: 4px;
  background: #F8F9FA;
}

/* 页面标题 */
.page-title {
  position: absolute;
  left: 140px;
  top: 46px;
  width: 96px;
  height: 26px;
  font-family: Inter;
  font-weight: 400;
  font-size: 16px;
  line-height: 26px;
  color: #323842;
}

/* 分类标题 */
.section-title {
  position: absolute;
  left: 22px;
  width: 28px;
  height: 22px;
  font-family: Inter;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  color: #171A1F;
}

.section-title:first-of-type {
  top: 100px;
}

.student-section {
  top: 242px;
}

/* 标签容器 */
.tags-container {
  position: absolute;
  left: 22px;
  width: 332px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tags-container:first-of-type {
  top: 138px;
}

.tags-container:last-of-type {
  top: 280px;
}

/* 标签项 */
.tag-item {
  width: 60px;
  height: 28px;
  cursor: pointer;
}

.tag-frame {
  width: 100%;
  height: 100%;
  background: #F2F2FD;
  border-radius: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tag-item.active .tag-frame {
  background: #636AE8;
}

.tag-text {
  font-family: Inter;
  font-weight: 400;
  font-size: 12px;
  line-height: 20px;
  color: #636AE8;
}

.tag-item.active .tag-text {
  color: #FFFFFF;
}

/* 底部按钮 */
.bottom-buttons {
  position: absolute;
  left: 13px;
  bottom: 77px;
  width: 350px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.btn-wechat,
.btn-manual {
  width: 100%;
  height: 52px;
  border-radius: 26px;
  border: 1px solid transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  cursor: pointer;
  font-family: Inter;
  font-weight: 400;
  font-size: 18px;
  line-height: 28px;
}

.btn-wechat {
  background: #22CCB2;
  color: #FFFFFF;
  box-shadow: 0px 4px 9px 0px rgba(34, 204, 178, 0.11), 0px 0px 2px 0px rgba(34, 204, 178, 0.12);
}

.btn-manual {
  background: #636AE8;
  color: #FFFFFF;
  box-shadow: 0px 4px 9px 0px rgba(99, 106, 232, 0.11), 0px 0px 2px 0px rgba(99, 106, 232, 0.12);
}

.btn-icon {
  width: 24px;
  height: 24px;
}
</style>
